import request from './axios';

/**
 * 会话管理相关API
 * 用于教师端课程详细页面的讨论模块
 */

/**
 * 查询会话列表
 * @param {Object} chatSession - 会话查询参数
 * @param {string} chatSession.createBy - 创建者
 * @param {string} chatSession.createTime - 创建时间
 * @param {string} chatSession.updateBy - 更新者
 * @param {string} chatSession.updateTime - 更新时间
 * @param {string} chatSession.remark - 备注
 * @param {Object} chatSession.params - 其他参数
 * @param {number} chatSession.id - 会话ID
 * @param {string} chatSession.name - 会话名，私聊无需名字
 * @returns {Promise<Object>} 会话列表响应数据
 */
export const getSessionList = (chatSession = {}) => {
  console.log('正在获取会话列表...', chatSession);
  
  // 构建查询参数，过滤掉空值
  const cleanParams = {};
  Object.keys(chatSession).forEach(key => {
    if (chatSession[key] !== null && chatSession[key] !== undefined && chatSession[key] !== '') {
      cleanParams[key] = chatSession[key];
    }
  });

  return request({
    url: '/core/session/list',
    method: 'get',
    params: cleanParams,
    // 使用表单编码格式，与接口文档保持一致
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  }).then(response => {
    console.log('获取会话列表成功:', response);

    // 检查响应格式，确保返回标准的TableDataInfo格式
    if (response && typeof response === 'object') {
      // 如果响应已经是标准格式（code为200表示成功）
      if (response.hasOwnProperty('total') && response.hasOwnProperty('rows') && response.hasOwnProperty('code')) {
        return {
          total: response.total || 0,
          rows: response.rows || [],
          code: response.code === 200 ? 0 : response.code, // 将200转换为0表示成功
          msg: response.msg || '成功'
        };
      }
      // 如果响应是嵌套格式，提取数据
      else if (response.data && response.data.hasOwnProperty('total') && response.data.hasOwnProperty('rows')) {
        return {
          total: response.data.total || 0,
          rows: response.data.rows || [],
          code: response.data.code === 200 ? 0 : (response.data.code || 0),
          msg: response.data.msg || '成功'
        };
      }
      // 如果响应只有数组数据
      else if (Array.isArray(response)) {
        return {
          total: response.length,
          rows: response,
          code: 0,
          msg: '成功'
        };
      }
      // 其他格式，尝试适配
      else {
        return {
          total: response.total || 0,
          rows: response.rows || response.data || [],
          code: response.code === 200 ? 0 : (response.code || 0),
          msg: response.msg || response.message || '成功'
        };
      }
    }

    // 如果响应格式不符合预期，返回空数据
    return {
      total: 0,
      rows: [],
      code: 0,
      msg: '成功'
    };
  }).catch(error => {
    console.error('获取会话列表失败:', error);
    // 返回错误格式，保持与TableDataInfo一致
    return {
      total: 0,
      rows: [],
      code: -1,
      msg: error.message || '获取会话列表失败'
    };
  });
};

/**
 * 创建新会话
 * @param {Object} sessionData - 会话数据
 * @param {string} sessionData.name - 会话名称
 * @param {string} sessionData.remark - 备注
 * @param {Array<number>} sessionData.memberIds - 会话成员ID列表
 * @param {number} sessionData.courseId - 课程ID（可选，用于课程相关会话）
 * @returns {Promise<Object>} 创建结果
 */
export const createSession = (sessionData) => {
  console.log('正在创建会话...', sessionData);

  // 构建符合API文档要求的ChatSessionVo对象
  const chatSessionVo = {
    name: sessionData.name || '',
    remark: sessionData.remark || '',
    memberIds: sessionData.memberIds || [],
    // 其他字段由后端自动填充
    createBy: '',
    createTime: '',
    updateBy: '',
    updateTime: '',
    params: sessionData.params || {},
    id: 0 // 新建会话时ID为0
  };

  // 如果有课程ID，添加到params中
  if (sessionData.courseId) {
    chatSessionVo.params.courseId = sessionData.courseId;
  }

  console.log('发送到API的ChatSessionVo对象:', chatSessionVo);

  return request({
    url: '/core/session',
    method: 'post',
    data: chatSessionVo,
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(response => {
    console.log('创建会话成功:', response);
    return response;
  }).catch(error => {
    console.error('创建会话失败:', error);
    throw error;
  });
};

/**
 * 更新会话信息
 * @param {Object} sessionData - 会话数据
 * @param {number} sessionData.id - 会话ID
 * @param {string} sessionData.name - 会话名称
 * @param {string} sessionData.remark - 备注
 * @returns {Promise<Object>} 更新结果
 */
export const updateSession = (sessionData) => {
  console.log('正在更新会话...', sessionData);
  
  return request({
    url: '/core/session',
    method: 'put',
    data: sessionData
  }).then(response => {
    console.log('更新会话成功:', response);
    return response;
  }).catch(error => {
    console.error('更新会话失败:', error);
    throw error;
  });
};

/**
 * 删除会话
 * @param {number|Array<number>} sessionIds - 会话ID或ID数组
 * @returns {Promise<Object>} 删除结果
 */
export const deleteSession = (sessionIds) => {
  console.log('正在删除会话...', sessionIds);
  
  // 处理单个ID或ID数组
  const ids = Array.isArray(sessionIds) ? sessionIds.join(',') : sessionIds;
  
  return request({
    url: `/core/session/${ids}`,
    method: 'delete'
  }).then(response => {
    console.log('删除会话成功:', response);
    return response;
  }).catch(error => {
    console.error('删除会话失败:', error);
    throw error;
  });
};

/**
 * 获取会话详情
 * @param {number} sessionId - 会话ID
 * @returns {Promise<Object>} 会话详情
 */
export const getSessionDetail = (sessionId) => {
  console.log('正在获取会话详情...', sessionId);
  
  return request({
    url: `/core/session/${sessionId}`,
    method: 'get'
  }).then(response => {
    console.log('获取会话详情成功:', response);
    return response;
  }).catch(error => {
    console.error('获取会话详情失败:', error);
    throw error;
  });
};

/**
 * 根据课程ID获取会话列表
 * @param {number|string} courseId - 课程ID
 * @param {Object} additionalParams - 额外的查询参数
 * @returns {Promise<Object>} 会话列表响应数据
 */
export const getSessionListByCourse = (courseId, additionalParams = {}) => {
  console.log('正在根据课程ID获取会话列表...', courseId, additionalParams);
  
  const params = {
    courseId: courseId,
    ...additionalParams
  };
  
  return getSessionList(params);
};

/**
 * 搜索会话
 * @param {Object} searchParams - 搜索参数
 * @param {string} searchParams.keyword - 搜索关键词
 * @param {number} searchParams.courseId - 课程ID
 * @param {number} searchParams.pageNum - 页码
 * @param {number} searchParams.pageSize - 每页大小
 * @returns {Promise<Object>} 搜索结果
 */
export const searchSessions = (searchParams) => {
  console.log('正在搜索会话...', searchParams);
  
  return getSessionList(searchParams);
};
